<?php

namespace app\api\controller;

use app\common\basics\Api;
use app\common\enum\ChatMsgEnum;
use app\common\model\kefu\ChatRecord;
use app\common\model\user\User;
use app\common\server\JsonServer;
use app\common\server\UrlServer;
use think\facade\Cache;

/**
 * 用户对用户聊天接口
 * Class UserChat
 * @package app\api\controller
 */
class UserChat extends Api
{
    /**
     * 获取用户聊天列表
     * @return \think\response\Json
     */
    public function chatList()
    {
        try {
            $page = $this->request->get('page/d', 1);
            $limit = $this->request->get('limit/d', 20);

            // 获取当前用户参与的所有聊天会话
            $chatSessions = ChatRecord::alias('cr')
                ->field('
                    CASE 
                        WHEN cr.from_id = ' . $this->user_id . ' THEN cr.to_id 
                        ELSE cr.from_id 
                    END as chat_user_id,
                    MAX(cr.create_time) as last_message_time,
                    COUNT(CASE WHEN cr.to_id = ' . $this->user_id . ' AND cr.is_read = 0 THEN 1 END) as unread_count
                ')
                ->where(function ($query) {
                    $query->where('cr.from_id', $this->user_id)
                          ->whereOr('cr.to_id', $this->user_id);
                })
                ->where('cr.from_type', 'user')
                ->where('cr.to_type', 'user')
                ->where('cr.shop_id', 0) // 用户对用户聊天shop_id为0
                ->group('chat_user_id')
                ->order('last_message_time', 'desc')
                ->page($page, $limit)
                ->select();

            $result = [];
            foreach ($chatSessions as $session) {
                // 获取聊天对象用户信息
                $chatUser = User::where('id', $session['chat_user_id'])
                    ->field('id,nickname,avatar')
                    ->findOrEmpty();

                if (!$chatUser->isEmpty()) {
                    // 获取最后一条消息
                    $lastMessage = ChatRecord::where(function ($query) use ($session) {
                        $query->where([
                            ['from_id', '=', $this->user_id],
                            ['to_id', '=', $session['chat_user_id']]
                        ])->whereOr([
                            ['from_id', '=', $session['chat_user_id']],
                            ['to_id', '=', $this->user_id]
                        ]);
                    })
                    ->where('from_type', 'user')
                    ->where('to_type', 'user')
                    ->where('shop_id', 0)
                    ->order('create_time', 'desc')
                    ->findOrEmpty();

                    $result[] = [
                        'user_id' => $chatUser['id'],
                        'nickname' => $chatUser['nickname'],
                        'avatar' => UrlServer::getFileUrl($chatUser['avatar']),
                        'last_message' => $lastMessage->isEmpty() ? '' : $this->formatMessage($lastMessage),
                        'last_message_time' => $session['last_message_time'],
                        'last_message_time_text' => date('Y-m-d H:i', $session['last_message_time']),
                        'unread_count' => $session['unread_count']
                    ];
                }
            }

            return JsonServer::success('获取成功', $result);
        } catch (\Exception $e) {
            return JsonServer::error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取与指定用户的聊天记录
     * @return \think\response\Json
     */
    public function chatHistory()
    {
        try {
            $chat_user_id = $this->request->get('chat_user_id/d', 0);
            $page = $this->request->get('page/d', 1);
            $limit = $this->request->get('limit/d', 20);

            if (empty($chat_user_id)) {
                return JsonServer::error('聊天用户ID不能为空');
            }

            // 验证聊天用户是否存在
            $chatUser = User::where('id', $chat_user_id)->findOrEmpty();
            if ($chatUser->isEmpty()) {
                return JsonServer::error('聊天用户不存在');
            }

            // 获取聊天记录
            $records = ChatRecord::where(function ($query) use ($chat_user_id) {
                $query->where([
                    ['from_id', '=', $this->user_id],
                    ['to_id', '=', $chat_user_id]
                ])->whereOr([
                    ['from_id', '=', $chat_user_id],
                    ['to_id', '=', $this->user_id]
                ]);
            })
            ->where('from_type', 'user')
            ->where('to_type', 'user')
            ->where('shop_id', 0)
            ->order('create_time', 'desc')
            ->page($page, $limit)
            ->select();

            $result = [];
            foreach ($records as $record) {
                // 获取发送者信息
                $fromUser = User::where('id', $record['from_id'])
                    ->field('id,nickname,avatar')
                    ->findOrEmpty();

                $result[] = [
                    'id' => $record['id'],
                    'from_id' => $record['from_id'],
                    'from_nickname' => $fromUser['nickname'] ?? '',
                    'from_avatar' => UrlServer::getFileUrl($fromUser['avatar'] ?? ''),
                    'to_id' => $record['to_id'],
                    'msg' => $record['msg'],
                    'msg_type' => $record['msg_type'],
                    'msg_type_text' => ChatMsgEnum::getMsgType($record['msg_type']),
                    'voice_duration' => $record['voice_duration'],
                    'is_read' => $record['is_read'],
                    'create_time' => $record['create_time'],
                    'create_time_text' => date('Y-m-d H:i:s', $record['create_time']),
                    'goods' => $record['msg_type'] == ChatMsgEnum::TYPE_GOODS ? json_decode($record['msg'], true) : null
                ];
            }

            // 标记消息为已读
            $updateCount = ChatRecord::where([
                ['from_id', '=', $chat_user_id],
                ['to_id', '=', $this->user_id],
                ['from_type', '=', 'user'],
                ['to_type', '=', 'user'],
                ['shop_id', '=', 0],
                ['is_read', '=', 0]
            ])->update(['is_read' => 1]);

            // 如果有消息被标记为已读，清除未读消息缓存
            if ($updateCount > 0) {
                self::clearUnreadBadgeCache($this->user_id);
            }

            return JsonServer::success('获取成功', array_reverse($result));
        } catch (\Exception $e) {
            return JsonServer::error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 搜索用户
     * @return \think\response\Json
     */
    public function searchUser()
    {
        try {
            $keyword = $this->request->get('keyword/s', '');
            $page = $this->request->get('page/d', 1);
            $limit = $this->request->get('limit/d', 20);

            if (empty($keyword)) {
                return JsonServer::error('搜索关键词不能为空');
            }

            $users = User::where('nickname|mobile', 'like', '%' . $keyword . '%')
                ->where('id', '<>', $this->user_id) // 排除自己
                ->where('disable', 0)
                ->where('del', 0)
                ->field('id,nickname,avatar,mobile')
                ->page($page, $limit)
                ->select();

            $result = [];
            foreach ($users as $user) {
                $result[] = [
                    'user_id' => $user['id'],
                    'nickname' => $user['nickname'],
                    'avatar' => UrlServer::getFileUrl($user['avatar']),
                    'mobile' => $user['mobile']
                ];
            }

            return JsonServer::success('搜索成功', $result);
        } catch (\Exception $e) {
            return JsonServer::error('搜索失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取消息角标（未读消息总数量）- 高效版本
     * @return \think\response\Json
     */
    public function getUnreadBadge()
    {
        try {
            // 缓存键名
            $cacheKey = 'user_unread_badge_' . $this->user_id;

            // 尝试从缓存获取
            $cachedResult = Cache::get($cacheKey);
            if ($cachedResult !== false && $cachedResult !== null) {
               
                return JsonServer::success('获取成功', ['unread_count' => (int)$cachedResult]);
            }

            // 使用ThinkPHP模型方式查询，更可靠
            $unreadCount = ChatRecord::where(function ($query) {
                $query->where([
                    ['shop_id', '=', 0],
                    ['shop_id', '=', 0],
                    ['is_read', '=', 0],
                    ['to_id', '=', $this->user_id],
                    ['from_type', '=', 'user'],
                    ['to_type', '=', 'user']
                ])->whereOr([
                    ['shop_id', '>', 0],
                    ['is_read', '=', 0],
                    ['to_id', '=', $this->user_id],
                    ['from_type', '=', 'kefu'],
                    ['to_type', '=', 'user']
                ]);
            })->count();

            // // 调试信息
            // trace('未读消息数量: ' . $unreadCount, 'info');
            // trace('用户ID: ' . $this->user_id, 'info');

            // 缓存结果30秒，避免频繁查询数据库
            // 使用字符串形式缓存，避免缓存系统对0值的特殊处理
            Cache::set($cacheKey, (string)$unreadCount, 100);

            return JsonServer::success('获取成功', ['unread_count' => $unreadCount]);
        } catch (\Exception $e) {
            return JsonServer::error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 清除用户未读消息缓存（在消息状态变更时调用）
     * @param int $user_id 用户ID
     * @return void
     */
    public static function clearUnreadBadgeCache($user_id)
    {
        $cacheKey = 'user_unread_badge_' . $user_id;
        Cache::delete($cacheKey);
    }

    /**
     * 格式化消息内容
     * @param $message
     * @return string
     */
    private function formatMessage($message)
    {
        switch ($message['msg_type']) {
            case ChatMsgEnum::TYPE_TEXT:
                return $message['msg'];
            case ChatMsgEnum::TYPE_IMG:
                return '[图片]';
            case ChatMsgEnum::TYPE_GOODS:
                return '[商品]';
            case ChatMsgEnum::TYPE_VOICE:
                return '[语音]';
            case ChatMsgEnum::TYPE_VIDEO:
                return '[视频]';
            case ChatMsgEnum::TYPE_ORDER:
                return '[订单]';
            default:
                return $message['msg'];
        }
    }



}
