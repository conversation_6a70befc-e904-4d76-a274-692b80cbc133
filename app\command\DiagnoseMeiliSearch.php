<?php

namespace app\command;

use app\common\library\MeiliSearch;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;

/**
 * 诊断MeiliSearch问题
 */
class DiagnoseMeiliSearch extends Command
{
    protected function configure()
    {
        $this->setName('diagnose:meilisearch')
            ->setDescription('诊断MeiliSearch搜索问题');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln("开始诊断MeiliSearch问题...\n");

        $issues = [];
        $suggestions = [];

        try {
            // 1. 检查MeiliSearch连接
            $output->writeln("1. 检查MeiliSearch连接...");
            $meili = new MeiliSearch();
            
            if (!$meili->testConnection()) {
                $issues[] = "MeiliSearch连接失败";
                $suggestions[] = "检查MeiliSearch服务是否运行，配置是否正确";
                $output->writeln("<error>✗ MeiliSearch连接失败</error>");
            } else {
                $output->writeln("<info>✓ MeiliSearch连接正常</info>");
            }

            // 2. 检查索引状态
            $output->writeln("\n2. 检查goods索引状态...");
            $indexInfo = $meili->getIndex('goods');
            
            if (isset($indexInfo['error'])) {
                $issues[] = "goods索引不存在";
                $suggestions[] = "运行命令创建索引: php think sync:goods-to-meilisearch";
                $output->writeln("<error>✗ goods索引不存在</error>");
            } else {
                $output->writeln("<info>✓ goods索引存在</info>");
                $output->writeln("索引信息: " . json_encode($indexInfo, JSON_UNESCAPED_UNICODE));
            }

            // 3. 检查商品数据同步
            $output->writeln("\n3. 检查商品数据同步状态...");
            
            // 数据库中的商品数量
            $dbGoodsCount = Db::name('goods')
                ->where('del', 0)
                ->where('status', 1)
                ->where('audit_status', 1)
                ->count();
            
            $output->writeln("数据库中符合条件的商品数量: " . $dbGoodsCount);
            
            // MeiliSearch中的商品数量
            $searchResult = $meili->searchInIndex('goods', '');
            $meiliGoodsCount = isset($searchResult['estimatedTotalHits']) ? $searchResult['estimatedTotalHits'] : 0;
            $output->writeln("MeiliSearch中的商品数量: " . $meiliGoodsCount);
            
            if ($dbGoodsCount > 0 && $meiliGoodsCount == 0) {
                $issues[] = "商品数据未同步到MeiliSearch";
                $suggestions[] = "运行同步命令: php think sync:goods-to-meilisearch";
                $output->writeln("<error>✗ 商品数据未同步</error>");
            } elseif ($dbGoodsCount > $meiliGoodsCount) {
                $issues[] = "MeiliSearch中的商品数量少于数据库";
                $suggestions[] = "重新同步: php think sync:goods-to-meilisearch --reset";
                $output->writeln("<comment>! 数据不完整，需要重新同步</comment>");
            } else {
                $output->writeln("<info>✓ 商品数据同步正常</info>");
            }

            // 4. 测试搜索功能
            $output->writeln("\n4. 测试搜索功能...");
            
            $testKeywords = ['手机', '衣服', '鞋子', '包包'];
            foreach ($testKeywords as $keyword) {
                $searchResult = $meili->searchInIndex('goods', $keyword);
                
                if (isset($searchResult['error'])) {
                    $issues[] = "搜索'{$keyword}'失败: " . $searchResult['error'];
                    $output->writeln("<error>✗ 搜索'{$keyword}'失败</error>");
                } else {
                    $hitCount = isset($searchResult['hits']) ? count($searchResult['hits']) : 0;
                    $output->writeln("<info>✓ 搜索'{$keyword}'成功，找到{$hitCount}个结果</info>");
                }
            }

            // 5. 检查索引配置
            $output->writeln("\n5. 检查索引配置...");
            
            // 获取索引设置
            $url = config('meilisearch.host', 'http://***************:7700') . "/indexes/goods/settings";
            $headers = [
                'Authorization: Bearer ' . config('meilisearch.api_key', '27bb9198372f81f8b95fb75d0252912de061fb6fa90d0ad6eb347cc051f0abe3'),
            ];
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode == 200) {
                $settings = json_decode($response, true);
                $output->writeln("<info>✓ 索引配置获取成功</info>");
                
                // 检查关键配置
                $searchableAttrs = $settings['searchableAttributes'] ?? [];
                if (!in_array('split_word', $searchableAttrs)) {
                    $issues[] = "索引配置缺少split_word字段";
                    $suggestions[] = "重新创建索引: php think sync:goods-to-meilisearch --reset";
                }
                
                $output->writeln("可搜索字段: " . implode(', ', $searchableAttrs));
            } else {
                $issues[] = "无法获取索引配置";
                $output->writeln("<error>✗ 无法获取索引配置</error>");
            }

            // 6. 输出诊断结果
            $output->writeln("\n" . str_repeat("=", 50));
            $output->writeln("诊断结果:");
            
            if (empty($issues)) {
                $output->writeln("<info>✓ 未发现问题，MeiliSearch工作正常</info>");
            } else {
                $output->writeln("<error>发现以下问题:</error>");
                foreach ($issues as $issue) {
                    $output->writeln("  - " . $issue);
                }
                
                $output->writeln("\n<comment>建议解决方案:</comment>");
                foreach ($suggestions as $suggestion) {
                    $output->writeln("  - " . $suggestion);
                }
            }

            return 0;

        } catch (\Exception $e) {
            $output->writeln("<error>诊断过程中发生异常: " . $e->getMessage() . "</error>");
            return 1;
        }
    }
}
