<?php

namespace app\command;

use app\common\library\MeiliSearch;
use think\console\Command;
use think\console\Input;
use think\console\Output;

/**
 * 仅修复MeiliSearch问题（不涉及数据库）
 */
class FixMeiliSearchOnly extends Command
{
    protected function configure()
    {
        $this->setName('fix:meilisearch-only')
            ->setDescription('仅修复MeiliSearch问题（不涉及数据库）')
            ->addOption('force', 'f', null, '强制重建索引');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln("开始修复MeiliSearch问题（仅MeiliSearch相关）...\n");
        
        $force = $input->getOption('force');

        try {
            // 1. 测试连接
            $output->writeln("1. 测试MeiliSearch连接...");
            $meili = new MeiliSearch();
            
            if (!$meili->testConnection()) {
                $output->writeln("<error>MeiliSearch连接失败，请检查服务状态和配置</error>");
                return 1;
            }
            $output->writeln("<info>✓ 连接正常</info>");

            // 2. 检查并修复索引
            $output->writeln("\n2. 检查goods索引...");
            $indexInfo = $meili->getIndex('goods');
            
            if (isset($indexInfo['error']) || $force) {
                if ($force) {
                    $output->writeln("强制重建索引...");
                } else {
                    $output->writeln("索引不存在，创建新索引...");
                }
                
                // 删除现有索引（如果存在）
                if (!isset($indexInfo['error'])) {
                    $output->writeln("删除现有索引...");
                    $meili->deleteIndex('goods');
                    sleep(2);
                }
                
                // 创建新索引
                $output->writeln("创建新索引...");
                $settings = [
                    'searchableAttributes' => [
                        'name',
                        'remark', 
                        'content',
                        'split_word'
                    ],
                    'filterableAttributes' => [
                        'shop_id',
                        'first_cate_id',
                        'second_cate_id', 
                        'third_cate_id',
                        'brand_id',
                        'status',
                        'audit_status',
                        'del',
                        'is_hot',
                        'is_recommend'
                    ],
                    'sortableAttributes' => [
                        'min_price',
                        'sales_actual',
                        'sales_virtual',
                        'create_time',
                        'update_time'
                    ],
                    'rankingRules' => [
                        'words',
                        'typo',
                        'proximity', 
                        'attribute',
                        'sort',
                        'exactness'
                    ]
                ];
                
                $result = $meili->createIndex('goods', $settings);
                if (isset($result['error'])) {
                    $output->writeln("<error>创建索引失败: " . $result['error'] . "</error>");
                    return 1;
                }
                
                sleep(3); // 等待索引创建完成
                $output->writeln("<info>✓ 索引创建成功</info>");
            } else {
                $output->writeln("<info>✓ 索引已存在</info>");
            }

            // 3. 添加测试数据
            $output->writeln("\n3. 添加测试数据...");
            
            $testData = [
                [
                    'id' => 1,
                    'name' => '测试商品1',
                    'remark' => '这是一个测试商品',
                    'content' => '测试商品详细描述',
                    'split_word' => '测试,商品,手机,电子产品',
                    'shop_id' => 1,
                    'first_cate_id' => 1,
                    'second_cate_id' => 1,
                    'third_cate_id' => 1,
                    'brand_id' => 1,
                    'min_price' => 99.00,
                    'market_price' => 199.00,
                    'sales_actual' => 100,
                    'sales_virtual' => 200,
                    'status' => 1,
                    'audit_status' => 1,
                    'del' => 0,
                    'is_hot' => 1,
                    'is_recommend' => 1,
                    'create_time' => time(),
                    'update_time' => time()
                ],
                [
                    'id' => 2,
                    'name' => '苹果手机',
                    'remark' => 'iPhone最新款',
                    'content' => '苹果手机详细描述',
                    'split_word' => '苹果,手机,iPhone,电子产品',
                    'shop_id' => 1,
                    'first_cate_id' => 1,
                    'second_cate_id' => 1,
                    'third_cate_id' => 1,
                    'brand_id' => 2,
                    'min_price' => 5999.00,
                    'market_price' => 6999.00,
                    'sales_actual' => 50,
                    'sales_virtual' => 100,
                    'status' => 1,
                    'audit_status' => 1,
                    'del' => 0,
                    'is_hot' => 1,
                    'is_recommend' => 1,
                    'create_time' => time(),
                    'update_time' => time()
                ]
            ];
            
            $response = $meili->importDocuments('goods', $testData, 'id');
            
            if (isset($response['taskUid'])) {
                $output->writeln("<info>✓ 测试数据添加成功，任务ID: " . $response['taskUid'] . "</info>");
            } else {
                $output->writeln("<error>测试数据添加失败: " . json_encode($response) . "</error>");
            }

            // 4. 验证修复结果
            $output->writeln("\n4. 验证修复结果...");
            
            // 等待数据索引完成
            sleep(2);
            
            // 测试搜索
            $testResult = $meili->searchInIndex('goods', '测试');
            if (isset($testResult['error'])) {
                $output->writeln("<error>搜索测试失败: " . $testResult['error'] . "</error>");
            } else {
                $hitCount = isset($testResult['hits']) ? count($testResult['hits']) : 0;
                $output->writeln("<info>✓ 搜索功能正常，找到 {$hitCount} 个结果</info>");
            }
            
            // 测试手机搜索
            $phoneResult = $meili->searchInIndex('goods', '手机');
            if (!isset($phoneResult['error'])) {
                $phoneCount = isset($phoneResult['hits']) ? count($phoneResult['hits']) : 0;
                $output->writeln("<info>✓ 搜索'手机'找到 {$phoneCount} 个结果</info>");
            }

            $output->writeln("\n<info>MeiliSearch修复完成！</info>");
            $output->writeln("现在可以测试搜索接口: api/search_record/searchGoods?keywords=手机");
            $output->writeln("或者: api/search_record/searchGoods?keywords=测试");
            
            return 0;

        } catch (\Exception $e) {
            $output->writeln("<error>修复过程中发生异常: " . $e->getMessage() . "</error>");
            return 1;
        }
    }
}
