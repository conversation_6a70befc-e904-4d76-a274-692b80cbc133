<?php
// 简单的MeiliSearch测试

$host = 'http://***************:7700';
$apiKey = '27bb9198372f81f8b95fb75d0252912de061fb6fa90d0ad6eb347cc051f0abe3';

echo "=== MeiliSearch搜索测试 ===\n\n";

function searchMeili($host, $apiKey, $keyword) {
    echo "搜索关键词: $keyword\n";
    
    $url = $host . '/indexes/goods/search';
    $headers = [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $apiKey,
    ];
    $data = json_encode(['q' => $keyword]);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "CURL错误: $error\n";
        return false;
    }
    
    echo "HTTP状态码: $httpCode\n";
    
    if ($httpCode == 200) {
        $result = json_decode($response, true);
        if (isset($result['hits'])) {
            $hitCount = count($result['hits']);
            echo "✓ 搜索成功，找到 {$hitCount} 个结果\n";
            
            foreach ($result['hits'] as $index => $item) {
                echo "  " . ($index + 1) . ". " . ($item['name'] ?? '未知商品') . " - 价格: " . ($item['min_price'] ?? '0') . "\n";
            }
            
            echo "处理时间: " . ($result['processingTimeMs'] ?? 0) . "ms\n";
            return true;
        } else {
            echo "✗ 搜索失败: " . $response . "\n";
            return false;
        }
    } else {
        echo "✗ HTTP请求失败: " . $response . "\n";
        return false;
    }
}

// 测试关键词
$testKeywords = ['手机', '测试', '苹果', '商品'];

foreach ($testKeywords as $keyword) {
    echo str_repeat("-", 40) . "\n";
    searchMeili($host, $apiKey, $keyword);
    echo "\n";
}

echo "=== 测试完成 ===\n";
echo "如果搜索成功，说明MeiliSearch工作正常。\n";
echo "现在可以测试完整的API接口了。\n";
?>
