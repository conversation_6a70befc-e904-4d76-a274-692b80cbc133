<?php
// 测试搜索API接口

// 模拟API请求
function testSearchAPI($keywords, $baseUrl = 'http://localhost') {
    echo "测试搜索关键词: $keywords\n";
    
    // 构建URL
    $url = $baseUrl . '/api/search_record/searchGoods?keywords=' . urlencode($keywords);
    echo "请求URL: $url\n";
    
    // 发送请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "CURL错误: $error\n";
        return false;
    }
    
    echo "HTTP状态码: $httpCode\n";
    echo "响应内容: $response\n";
    
    if ($httpCode == 200) {
        $data = json_decode($response, true);
        if ($data && isset($data['code']) && $data['code'] == 1) {
            echo "✓ 搜索成功\n";
            if (isset($data['data']['list'])) {
                echo "找到 " . count($data['data']['list']) . " 个商品\n";
                foreach ($data['data']['list'] as $index => $item) {
                    echo "  " . ($index + 1) . ". " . ($item['name'] ?? '未知商品') . " - 价格: " . ($item['min_price'] ?? '0') . "\n";
                }
            }
            return true;
        } else {
            echo "✗ 搜索失败: " . ($data['msg'] ?? '未知错误') . "\n";
            return false;
        }
    } else {
        echo "✗ HTTP请求失败\n";
        return false;
    }
}

// 直接测试MeiliSearch
function testMeiliSearchDirect($keywords) {
    echo "\n直接测试MeiliSearch:\n";
    
    require_once 'app/common/library/MeiliSearch.php';
    
    $meili = new \app\common\library\MeiliSearch();
    
    $result = $meili->searchInIndex('goods', $keywords);
    
    if (isset($result['error'])) {
        echo "✗ MeiliSearch搜索失败: " . $result['error'] . "\n";
        return false;
    } else {
        $hitCount = isset($result['hits']) ? count($result['hits']) : 0;
        echo "✓ MeiliSearch搜索成功，找到 {$hitCount} 个结果\n";
        
        if (isset($result['hits'])) {
            foreach ($result['hits'] as $index => $item) {
                echo "  " . ($index + 1) . ". " . ($item['name'] ?? '未知商品') . " - 价格: " . ($item['min_price'] ?? '0') . "\n";
            }
        }
        return true;
    }
}

echo "=== 搜索接口测试 ===\n\n";

// 测试关键词列表
$testKeywords = ['手机', '测试', '苹果', '商品'];

foreach ($testKeywords as $keyword) {
    echo str_repeat("-", 50) . "\n";
    
    // 直接测试MeiliSearch
    testMeiliSearchDirect($keyword);
    
    echo "\n";
}

echo "\n=== 测试完成 ===\n";
echo "如果MeiliSearch测试成功，说明搜索功能已经修复。\n";
echo "现在可以通过浏览器或API客户端测试完整的搜索接口:\n";
echo "GET /api/search_record/searchGoods?keywords=手机\n";
?>
