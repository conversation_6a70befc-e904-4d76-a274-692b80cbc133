<?php

namespace app\command;

use app\common\library\MeiliSearch;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;

/**
 * 修复MeiliSearch常见问题
 */
class FixMeiliSearch extends Command
{
    protected function configure()
    {
        $this->setName('fix:meilisearch')
            ->setDescription('自动修复MeiliSearch常见问题')
            ->addOption('force', 'f', null, '强制重建索引');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln("开始修复MeiliSearch问题...\n");
        
        $force = $input->getOption('force');

        try {
            // 1. 测试连接
            $output->writeln("1. 测试MeiliSearch连接...");
            $meili = new MeiliSearch();
            
            if (!$meili->testConnection()) {
                $output->writeln("<error>MeiliSearch连接失败，请检查服务状态和配置</error>");
                return 1;
            }
            $output->writeln("<info>✓ 连接正常</info>");

            // 2. 检查并修复索引
            $output->writeln("\n2. 检查goods索引...");
            $indexInfo = $meili->getIndex('goods');
            
            if (isset($indexInfo['error']) || $force) {
                if ($force) {
                    $output->writeln("强制重建索引...");
                } else {
                    $output->writeln("索引不存在，创建新索引...");
                }
                
                // 删除现有索引（如果存在）
                if (!isset($indexInfo['error'])) {
                    $output->writeln("删除现有索引...");
                    $meili->deleteIndex('goods');
                    sleep(2);
                }
                
                // 创建新索引
                $output->writeln("创建新索引...");
                $settings = [
                    'searchableAttributes' => [
                        'name',
                        'remark', 
                        'content',
                        'split_word'
                    ],
                    'filterableAttributes' => [
                        'shop_id',
                        'first_cate_id',
                        'second_cate_id', 
                        'third_cate_id',
                        'brand_id',
                        'status',
                        'audit_status',
                        'del',
                        'is_hot',
                        'is_recommend'
                    ],
                    'sortableAttributes' => [
                        'min_price',
                        'sales_actual',
                        'sales_virtual',
                        'create_time',
                        'update_time'
                    ],
                    'rankingRules' => [
                        'words',
                        'typo',
                        'proximity', 
                        'attribute',
                        'sort',
                        'exactness'
                    ]
                ];
                
                $result = $meili->createIndex('goods', $settings);
                if (isset($result['error'])) {
                    $output->writeln("<error>创建索引失败: " . $result['error'] . "</error>");
                    return 1;
                }
                
                sleep(3); // 等待索引创建完成
                $output->writeln("<info>✓ 索引创建成功</info>");
            } else {
                $output->writeln("<info>✓ 索引已存在</info>");
            }

            // 3. 同步商品数据
            $output->writeln("\n3. 同步商品数据...");
            
            // 检查数据库中的商品数量
            $dbCount = Db::name('goods')
                ->where('del', 0)
                ->where('status', 1) 
                ->where('audit_status', 1)
                ->count();
            
            $output->writeln("数据库中符合条件的商品数量: " . $dbCount);
            
            if ($dbCount == 0) {
                $output->writeln("<comment>数据库中没有符合条件的商品</comment>");
                return 0;
            }
            
            // 分批同步商品
            $batchSize = 100;
            $totalBatches = ceil($dbCount / $batchSize);
            $synced = 0;
            
            for ($batch = 0; $batch < $totalBatches; $batch++) {
                $offset = $batch * $batchSize;
                
                $goods = Db::name('goods')
                    ->where('del', 0)
                    ->where('status', 1)
                    ->where('audit_status', 1)
                    ->field('id, name, image, remark, content, split_word, shop_id, first_cate_id, second_cate_id, third_cate_id, brand_id, min_price, market_price, sales_actual, sales_virtual, is_hot, is_recommend, create_time, update_time')
                    ->limit($offset, $batchSize)
                    ->select()
                    ->toArray();
                
                if (empty($goods)) {
                    break;
                }
                
                // 准备文档数据
                $documents = [];
                foreach ($goods as $item) {
                    $doc = [];
                    foreach ($item as $key => $value) {
                        if (is_array($value)) {
                            $doc[$key] = json_encode($value);
                        } else {
                            $doc[$key] = $value;
                        }
                    }
                    $documents[] = $doc;
                }
                
                // 导入到MeiliSearch
                $response = $meili->importDocuments('goods', $documents, 'id');
                
                if (isset($response['taskUid'])) {
                    $synced += count($documents);
                    $output->writeln("批次 " . ($batch + 1) . "/{$totalBatches} 同步成功，已同步 {$synced}/{$dbCount} 个商品");
                } else {
                    $output->writeln("<error>批次 " . ($batch + 1) . " 同步失败: " . json_encode($response) . "</error>");
                }
                
                // 短暂休息
                usleep(100000); // 0.1秒
            }
            
            $output->writeln("<info>✓ 商品数据同步完成，共同步 {$synced} 个商品</info>");

            // 4. 验证修复结果
            $output->writeln("\n4. 验证修复结果...");
            
            // 测试搜索
            $testResult = $meili->searchInIndex('goods', '测试');
            if (isset($testResult['error'])) {
                $output->writeln("<error>搜索测试失败: " . $testResult['error'] . "</error>");
            } else {
                $output->writeln("<info>✓ 搜索功能正常</info>");
            }
            
            // 检查数据量
            $searchAll = $meili->searchInIndex('goods', '');
            $meiliCount = isset($searchAll['estimatedTotalHits']) ? $searchAll['estimatedTotalHits'] : 0;
            $output->writeln("MeiliSearch中的商品数量: " . $meiliCount);
            
            if ($meiliCount >= $dbCount * 0.9) { // 允许10%的误差
                $output->writeln("<info>✓ 数据同步完整</info>");
            } else {
                $output->writeln("<comment>! 数据可能不完整，建议重新同步</comment>");
            }

            $output->writeln("\n<info>MeiliSearch修复完成！</info>");
            $output->writeln("现在可以测试搜索接口: api/search_record/searchGoods");
            
            return 0;

        } catch (\Exception $e) {
            $output->writeln("<error>修复过程中发生异常: " . $e->getMessage() . "</error>");
            return 1;
        }
    }
}
