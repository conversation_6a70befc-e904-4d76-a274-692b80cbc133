var lock = {};
var load = {};

var like = {
    ajax: function (json) {
        var load_index = null;
        if (json.beforeSend === undefined) {
            if (lock[json.url.replace("/", "_")] !== undefined) {
                return
            }
            lock[json.url.replace("/", "_")] = true;
            json.beforeSend = function () {
                load[json.url.replace("/", "_")] = setTimeout(function () {
                    load_index = layer.load(1, {shade: [0.1, "#fff"]})
                }, 1500)
            }
        }

        if (json.error === undefined) {
            json.error = function (res) {
                layer.msg("网络错误", {offset: "240px", icon: 2, time: 1500}, function () {
                    return
                })
            }
        }
        if (json.timeout === undefined) {
            json.timeout = 30000
        }
        if (json.type === undefined) {
            json.type = "get"
        }
        json.complete = function (xhr, status) {
            delete lock[json.url.replace("/", "_")];
            if (status == "timeout") {
                layer.msg("请求超时，请重试", {offset: "240px", icon: 2, time: 1500}, function () {
                    if (load_index !== undefined) {
                        layer.close(load_index)
                    }
                    return
                });
                return
            }
            clearTimeout(load[json.url.replace("/", "_")]);
            if (load_index !== undefined) {
                layer.close(load_index)
            }
            res = xhr.responseJSON;
            if (res !== undefined && res.code == -1) {
                window.location.href = window.location.href
            }
            if (res !== undefined && res.code == 0 && res.show == 1) {
                layer.msg(res.msg, {offset: "240px", icon: 2, time: 1500}, function () {
                    return;
                })
            }
        };
        $.ajax(json);
    },
    tableLists: function (elem, url, cols, where,page = true) {
        where = where === undefined ? {} : where;
        page = page === undefined ? true : page;
        layui.use(['table'], function () {
            var table = layui.table;
            table.render({
                elem: elem
                , url: url
                , cols: [cols]
                , where: where
                , text: {none: '暂无相关数据'}
                , response: {
                    statusCode: 1
                }
                , escape: true
                , page: page
                , parseData: function (res) {
                    return {
                        "code": res.code,
                        "msg": res.msg,
                        "count": res.data.count,
                        "data": res.data.lists
                    };
                }
                , done: function () {
                    setTimeout(function () {
                        $(".layui-table-main tr").each(function (index, val) {
                            $($(".layui-table-fixed-l .layui-table-body tbody tr")[index]).height($(val).height());
                            $($(".layui-table-fixed-r .layui-table-body tbody tr")[index]).height($(val).height());
                        });
                    }, 200);
                }
            });
        });
    },
    keyUpClick: function (elem, target) {
        $(elem).keyup(function (event) {
            if (event.keyCode === 13) {
                $(target).trigger('click');
            }
        });
    },
    eventClick: function (active) {
        $(document).on("click", ".layEvent", function () {
            var type = $(this).attr("lay-event");
            var obj  = $(this).attr("lay-data");

            active[type] ? active[type].call(this, obj) : "";
        });

        layui.use(["table"], function () {
            layui.table.on("tool(like-table-lists)", function(obj){
                var type = obj.event;
                active[type] ? active[type].call(this, obj) : "";
            });
        });
    },
    setSelect: function (defaultId, data, targetName, optionName) {
        layui.use(['form'], function () {
            var form = layui.form;
            var selectHtml = '<option value="">请选择'+optionName+'</option>';

            // 如果传入了parentId参数且parentId为空，则只显示"请选择"选项，不显示数据选项
            // 这样可以避免在级联选择中，当上级选择"请选择XX"时，下级显示错误数据的问题
            if (parentId !== undefined && (parentId === '' || parentId === null || parentId === 0)) {
                // 只保留"请选择"选项，不添加数据选项
            } else {
                // 正常情况下添加数据选项
                for (var i in data) {
                    selectHtml += '<option value="' + data[i]['id'] + '">' + data[i]['name'] + '</option>';
                }
            }

            $('select[name=' + targetName + ']').html(selectHtml);
            $('select[name=' + targetName + ']').val(defaultId);
            form.render('select');
        });
    },
    /**
     * 上传图片
     * <AUTHOR>
     * @param options
     */
    imageUpload: function (options) {
        var that  = options.that;
        var limit = options.limit || 1;
        var field = options.field || "image";
        var content = options.content || '/admin/file/lists?type=10&&shop_id=0';
        var isSpecImage = options.isSpecImage || false;

        parent.layer.open({
            type: 2
            ,title: "上传图片"
            ,shadeClose: true
            ,maxmin: true
            ,anim: 1
            ,shade: 0.3
            ,area: ["90%", "90%"]
            ,content: content,
            success: function (layero, index) {
                var iframeNode = $(layero).find("iframe").contents();

                iframeNode.find("#okFile").click(function () {
                    var fileUrls = iframeNode.find("#fileUrl").val();
                    var urls = fileUrls.split(',');

                    if (urls.length <= 0) {
                        parent.layer.msg("请至少选择一张图片");
                        return false;
                    }

                    if (urls.length > limit) {
                        parent.layer.msg("限制只能选"+limit+"张");
                        return false;
                    }

                    if (isSpecImage) {
                        urls.forEach(function (url) {
                            // 商品多规格特殊处理
                            var template  = '<div class="upload-image-div">';
                            template += '<img src="'+url+'" alt="img" class="preview-image" data-src="'+url+'" title="点击放大查看">';
                            template += '<div class="del-upload-btn">x</div>';
                            that.parent().before(template);
                            that.parent().parent().find(".upload-spec-image").val(url);
                        });
                    } else {
                        urls.forEach(function (url, index) {
                            var template  = '<div class="upload-image-div">';
                            template += '<img src="'+url+'" alt="img" class="preview-image" data-src="'+url+'" title="点击放大查看">';
                            template += '<input type="hidden" name="'+field+'" value="'+url+'">';
                            template += '<div class="del-upload-btn">×</div>';
                            template += '<div class="image-controls"></div>';
                            template += '</div>';
                            that.parent().before(template);
                        });

                        // 上传完成后，重新初始化功能
                        setTimeout(function() {
                            if (typeof window.updateImageControls === 'function') {
                                window.updateImageControls();
                            }
                            if (typeof window.initImageSortable === 'function') {
                                window.initImageSortable();
                            }
                            if (typeof window.updateImageOrder === 'function') {
                                window.updateImageOrder();
                            }
                        }, 100);
                    }

                    if (limit === 1) that.parent().hide();
                    parent.layer.close(index);
                });
            }
        });
    },
    /**
     * 上传视频
     * <AUTHOR>
     * @param options
     */
    videoUpload: function (options) {
        var that  = options.that;
        var limit = options.limit || 1;
        var field = options.field || "video";
        var content = options.content || '/admin/file/lists?type=20';

        parent.layer.open({
            type: 2
            ,title: "上传视频"
            ,shadeClose: true
            ,maxmin: true
            ,anim: 1
            ,shade: 0.3
            ,area: ["90%", "90%"]
            ,content: content
            ,success: function (layero, index) {
                var iframeNode = $(layero).find("iframe").contents();
                iframeNode.find("#okFile").click(function () {
                    var urls = [];
                    iframeNode.find(".warehouse li.on").each(function () {
                        urls.push($(this).attr("lay-href"));
                    });

                    if (urls.length <= 0) {
                        parent.layer.msg("请至少选择一个视频");
                        return false;
                    }

                    if (urls.length > limit) {
                        parent.layer.msg("限制只能选"+limit+"个");
                        return false;
                    }

                    urls.forEach(function (url) {
                        var template  = '<div class="upload-video-div">';
                        template += '<video src="'+url+'"></video>';
                        template += '<input type="hidden" name="'+field+'" value="'+url+'">';
                        template += '<div class="del-upload-btn" style="z-index: 999;">×</div>';

                        that.parent().parent().prepend(template);
                    });

                    if (limit === 1) that.parent().hide();
                    parent.layer.close(index);
                });
            }
        });
    },
    /**
     * 删除上传文件
     * <AUTHOR>
     */
    delUpload: function () {
        $(document).on("click", ".del-upload-btn", function () {
            var container = $(this).parent().parent();
            var $uploadDiv = $(this).parent();

            // 检查是否是视频删除
            var isVideo = $uploadDiv.hasClass('upload-video-div') ||
                         $uploadDiv.find('video').length > 0 ||
                         container.attr('id') === 'videoContainer';

            if (isVideo) {
                // 视频删除的特殊处理
                layer.confirm('确定要删除这个视频吗？', {
                    icon: 3,
                    title: '确认删除'
                }, function(index) {
                    // 移除视频元素
                    $uploadDiv.remove();

                    // 恢复添加视频按钮
                    container.find('.like-upload-video .upload-image-elem').show();

                    layer.close(index);
                    layer.msg('视频已删除');
                });
                return; // 阻止默认处理
            }

            // 显示添加图片按钮
            container.find(".upload-image-elem").show();

            // 清空规格图片值（商品多规格特殊处理）
            container.find(".upload-spec-image").val('');

            // 移除图片元素
            $(this).parent().remove();

            // 针对商品编辑页面的特殊处理
            if (container.attr('id') === 'imageContainer' ||
                container.attr('id') === 'posterContainer') {
                // 检查是否需要重置图片容器
                if (container.find('.upload-image-elem').length === 0) {
                    // 重新添加图片上传按钮
                    var fieldId = container.attr('id').replace('Container', '');
                    var html = '<div class="like-upload-image">' +
                               '<div class="upload-image-elem">' +
                               '<a class="add-upload-image" id="' + fieldId + '"> + 添加图片</a>' +
                               '</div></div>';
                    container.html(html);
                }
            } else if (container.attr('id') === 'videoContainer') {
                // 检查是否需要重置视频容器
                if (container.find('.upload-image-elem').length === 0) {
                    // 重新添加视频上传按钮
                    var html = '<div class="like-upload-video">' +
                               '<div class="upload-image-elem">' +
                               '<a class="add-upload-video" id="video"> + 添加视频</a>' +
                               '</div></div>';
                    container.html(html);
                }
            } else if (container.attr('id') === 'goodsImageContainer') {
                // 检查是否需要重置轮播图容器
                if (container.find('.upload-image-elem').length === 0) {
                    // 重新添加轮播图上传按钮
                    var html = '<div class="like-upload-image sortable-upload-container">' +
                               '<div class="upload-image-elem">' +
                               '<a class="add-upload-image" id="goodsimage"> + 添加图片</a>' +
                               '</div></div>';
                    container.html(html);
                }
            }
        });
    },
    /**
     * 放大图片
     */
    showImg: function (url, xp) {
        function getImageWidth(url, callback) {
            var img = new Image();
            img.src = url;
            if (img.complete) {
                callback(img.width, img.height)
            } else {
                img.onload = function () {
                    callback(img.width, img.height)
                }
            }
        }
        // 默认最大尺寸改为250px（原来是500px的50%）
        xp = xp === undefined ? 250 : xp * 0.5;
        getImageWidth(url, function (width, height) {
            if (height > xp) {
                var ratio = width / height;
                height = xp;
                width = height * ratio
            }
            if (width > xp) {
                var ratio = height / width;
                width = xp;
                height = width * ratio
            }
            layer.closeAll();
            layer.open({
                type: 1,
                closeBtn: 1,
                shade: false,
                title: false,
                shadeClose: true, // 允许点击遮罩关闭
                area: ["auto", "auto"],
                content: '<img src="' + url + '" width="' + width + 'px" height="' + height + 'px" style="display:block;">'
            })
        })
    },

    /**
     * 商品图片预览 - 统一的图片点击放大方法
     * @param {string} url 图片URL
     * @param {number} maxSize 最大显示尺寸，默认500px
     */
    goodsImagePreview: function(url, maxSize) {
        // 防止重复调用
        if (this._previewLock) {
            return;
        }
        this._previewLock = true;

        // 防止重复弹窗，先关闭所有现有的弹窗
        layer.closeAll();

        if (!url) {
            console.warn('图片URL为空');
            this._previewLock = false;
            return;
        }

        var self = this;
        var defaultMaxSize = maxSize || 500;

        function getImageDimensions(imageUrl, callback) {
            var img = new Image();
            img.onload = function() {
                callback(this.width, this.height);
            };
            img.onerror = function() {
                console.error('图片加载失败:', imageUrl);
                layer.msg('图片加载失败');
                self._previewLock = false;
            };
            img.src = imageUrl;
        }

        getImageDimensions(url, function(originalWidth, originalHeight) {
            // 计算显示尺寸，保持宽高比
            var scale = Math.min(
                defaultMaxSize / originalWidth,
                defaultMaxSize / originalHeight,
                1
            );

            var displayWidth = Math.floor(originalWidth * scale);
            var displayHeight = Math.floor(originalHeight * scale);

            // 确保最小尺寸和最大尺寸
            if (displayWidth < 200) displayWidth = 200;
            if (displayHeight < 200) displayHeight = 200;
            if (displayWidth > 800) displayWidth = 800;
            if (displayHeight > 600) displayHeight = 600;

            layer.open({
                type: 1,
                title: false,
                closeBtn: 1,
                area: [displayWidth + 'px', displayHeight + 'px'],
                skin: 'layui-layer-nobg',
                shadeClose: true,
                resize: false,
                move: '.layui-layer-title',
                content: '<div style="text-align: center; background: #fff; padding: 5px;"><img src="' + url + '" style="max-width: 100%; max-height: 100%; display: block; margin: 0 auto; border-radius: 4px;" /></div>',
                success: function(layero, index) {
                    // 添加点击图片关闭功能
                    layero.find('img').on('click', function(e) {
                        e.stopPropagation();
                        layer.close(index);
                    });

                    // 添加键盘ESC关闭功能
                    $(document).on('keyup.goodsPreview_' + index, function(e) {
                        if (e.keyCode === 27) { // ESC键
                            layer.close(index);
                        }
                    });
                },
                end: function() {
                    // 清理键盘事件监听和锁定状态
                    $(document).off('keyup.goodsPreview_' + layer.index);
                    self._previewLock = false;
                }
            });
        });
    },

    certUpload:function (element, url, domain) {
        layui.upload.render({
            elem: element
            ,url: url
            ,accept:'file'
            ,exts:'pem|txt|doc'
            ,done: function(res, index, upload) {
                var html = '<div class="pay-li">\n' +
                    '<img class="pay-img" ' +
                    'src="/static/common/image/default/upload.png">' +
                    '<a class="pay-img-del-x" style="display: none">x</a>' +
                    '</div>';
                $(element).prev().val(res.data[1].uri);
                $(element).after(html);
                $(element).css('display','none');
            }
        });
    },
    /**
     * @notes 通用支付函数
     * @param {number} pay_way 支付方式 (需要与后端 PayEnum 对应, 例如 1:微信, 2:支付宝, 3:余额)
     * @param {object} pay_data 后端返回的支付参数 (结构因支付方式而异)
     * @param {function} success_callback 支付成功回调
     * @param {function} error_callback 支付失败或取消回调
     * <AUTHOR>
     */
    pay: function (pay_way, pay_data, success_callback, error_callback) {
        // 确保回调是函数
        success_callback = typeof success_callback === 'function' ? success_callback : function() { layer.msg('支付成功', {icon: 1}); };
        error_callback = typeof error_callback === 'function' ? error_callback : function(err) { layer.msg(err || '支付失败或取消', {icon: 2}); };

        // --- 假设的支付方式常量值 (请根据 PayEnum.php 修改) ---
        const PAY_WAY_WECHAT = 1;
        const PAY_WAY_ALIPAY = 2;
        const PAY_WAY_BALANCE = 3;
        // --- End Constants ---

        switch(parseInt(pay_way)) {
            case PAY_WAY_WECHAT:
                // 微信支付逻辑
                if (typeof WeixinJSBridge == "undefined"){
                    // 检查微信环境
                    if( document.addEventListener ){
                        document.addEventListener('WeixinJSBridgeReady', function(){ like.callWechatPay(pay_data, success_callback, error_callback); }, false);
                    }else if (document.attachEvent){
                        document.attachEvent('WeixinJSBridgeReady', function(){ like.callWechatPay(pay_data, success_callback, error_callback); }); 
                        document.attachEvent('onWeixinJSBridgeReady', function(){ like.callWechatPay(pay_data, success_callback, error_callback); });
                    }
                 }else{
                    // 直接调用微信支付
                    like.callWechatPay(pay_data, success_callback, error_callback);
                 }
                break;

            case PAY_WAY_ALIPAY:
                // 支付宝支付逻辑
                // 通常后端会返回一个包含 form 表单的 html 字符串
                if (pay_data && pay_data.html) { 
                    // 创建一个隐藏的 div 并插入表单，然后提交
                    $('body').append('<div id="alipay_submit_hidden" style="display:none">' + pay_data.html + '</div>');
                    $('#alipay_submit_hidden form').submit();
                    // 注意：支付宝支付通常会跳转页面，成功回调可能需要在支付成功后的跳转页面处理
                    // 这里可以认为发起跳转即为开始支付，如果需要更精确的回调，需要后端配合页面跳转和通知
                    // 暂时不调用 success_callback，留给后续页面处理
                    // 如果后端仅返回支付URL，则使用 window.location.href = pay_data.pay_url;
                } else {
                    error_callback('支付宝支付参数错误');
                }
                break;

            case PAY_WAY_BALANCE:
                // 余额支付逻辑
                // 余额支付通常后端直接完成扣款，前端只需要提示成功即可
                // 后端应该在返回支付参数时就确认扣款成功，如果失败会直接返回错误
                // 所以这里可以直接调用成功回调
                success_callback();
                break;

            default:
                error_callback('不支持的支付方式: ' + pay_way);
                break;
        }
    },

    /**
     * @notes 内部函数：调用微信 JSAPI 支付
     * @param {object} pay_data JSSDK 所需的支付参数 (appId, timeStamp, nonceStr, package, signType, paySign)
     * @param {function} success_callback 
     * @param {function} error_callback 
     */
    callWechatPay: function(pay_data, success_callback, error_callback) {
         if (!pay_data || !pay_data.appId || !pay_data.timeStamp || !pay_data.nonceStr || !pay_data.package || !pay_data.signType || !pay_data.paySign) {
            error_callback('微信支付参数不完整');
            return;
         }
         WeixinJSBridge.invoke(
            'getBrandWCPayRequest', 
            pay_data, // 后端返回的支付参数
            function(res){
                // 使用以上方式判断前端返回,微信团队郑重提示：
                //res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
                if(res.err_msg == "get_brand_wcpay_request:ok" ) {
                    success_callback();
                } else if (res.err_msg == "get_brand_wcpay_request:cancel") {
                    error_callback('支付已取消');
                } else {
                    // get_brand_wcpay_request:fail 或其他错误
                    error_callback(res.err_desc || res.err_msg || '支付失败');
                }
            }
        );
    }
};

// 商品图片点击放大功能 - 使用统一的预览方法
$(document).ready(function() {
    var clickTimeout = null;

    // 为商品图片添加点击放大功能
    $(document).on('click', '.preview-image', function(e) {
        e.preventDefault();
        e.stopPropagation();

        // 防止快速重复点击
        if (clickTimeout) {
            clearTimeout(clickTimeout);
        }

        var $this = $(this);
        clickTimeout = setTimeout(function() {
            var imgSrc = $this.data('src') || $this.attr('src');
            if (imgSrc) {
                // 使用新的统一预览方法，避免重复弹窗
                like.goodsImagePreview(imgSrc, 600);
            }
            clickTimeout = null;
        }, 100); // 100ms 防抖
    });

    // 为图片添加鼠标悬停效果
    $(document).on('mouseenter', '.preview-image', function() {
        $(this).css({
            'cursor': 'pointer',
            'opacity': '0.8',
            'transform': 'scale(1.02)',
            'transition': 'all 0.2s ease'
        });
    }).on('mouseleave', '.preview-image', function() {
        $(this).css({
            'opacity': '1',
            'transform': 'scale(1)',
            'transition': 'all 0.2s ease'
        });
    });
});




// 监听商品表单提交
layui.use(['form'], function () {
    var form = layui.form;
    var $ = layui.jquery;
    form.on('submit(goods-submit)', function (data) {
        var field = data.field;
        var spec_data = [];
        if (field.spec_type == 2) {
            $('#more-spec-lists-table tbody tr').each(function () {
                var item = {};
                $(this).find('input').each(function () {
                    item[$(this).attr('name')] = $(this).val();
                });
                spec_data.push(item);
            });
            field.spec_data = JSON.stringify(spec_data);
        }

        var imageUrls = [];
        $('#goodsImageContainer .upload-image-div input[name="goods_image[]"]').each(function() {
            imageUrls.push($(this).val());
        });
        field.goods_image = imageUrls.join(',');


        var loading = layer.load(1, {shade: [0.1, '#fff']});
        $.ajax({
            url: data.form.action,
            type: 'post',
            data: field,
            dataType: 'json',
            success: function (res) {
                layer.close(loading);
                if (res.code === 1) {
                    layer.msg(res.msg, {icon: 1, time: 1000}, function () {
                        parent.layer.close(parent.layer.getFrameIndex(window.name));
                        parent.layui.table.reload('like-table-lists');
                    });
                } else {
                    layer.msg(res.msg, {icon: 2, time: 2000});
                }
            },
            error: function (xhr, status, error) {
                layer.close(loading);
                layer.msg('请求失败: ' + error, {icon: 2});
            }
        });
        return false;
    });
});
